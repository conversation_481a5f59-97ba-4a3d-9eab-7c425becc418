<!DOCTYPE html>
<html>
<head>
    <title>简单跳跃游戏</title>
    <style>
        #game {
            width: 400px;
            height: 300px;
            background: linear-gradient(#87CEEB, #90EE90);
            position: relative;
            overflow: hidden;
            border: 2px solid #000;
        }
        .player {
            width: 20px;
            height: 20px;
            background: red;
            position: absolute;
            bottom: 0;
            left: 50px;
        }
        .platform {
            width: 50px;
            height: 10px;
            background: brown;
            position: absolute;
        }
    </style>
</head>
<body>
    <div id="app">
        <div id="game">
            <div class="player" :style="{ bottom: playerY + 'px', left: playerX + 'px' }"></div>
            <div class="platform" style="bottom: 50px; left: 100px;"></div>
            <div class="platform" style="bottom: 100px; left: 200px;"></div>
        </div>
    </div>

    <script src="https://unpkg.com/vue@3"></script>
    <script>
        const { createApp, ref } = Vue;

        createApp({
            setup() {
                const playerX = ref(50);
                const playerY = ref(0);
                const velocityY = ref(0);
                const gravity = 0.5;
                const jumpForce = -10;
                let isJumping = false;

                const checkCollision = () => {
                    const player = document.querySelector('.player');
                    const platforms = document.querySelectorAll('.platform');
                    let standing = false;

                    platforms.forEach(platform => {
                        const playerRect = player.getBoundingClientRect();
                        const platformRect = platform.getBoundingClientRect();
                        if (
                            playerRect.bottom >= platformRect.top &&
                            playerRect.top <= platformRect.bottom &&
                            playerRect.right >= platformRect.left &&
                            playerRect.left <= platformRect.right
                        ) {
                            if (velocityY.value > 0) {
                                playerY.value = platformRect.top - playerRect.height;
                                velocityY.value = 0;
                                isJumping = false;
                                standing = true;
                            }
                        }
                    });
                    return standing;
                };

                const update = () => {
                    velocityY.value += gravity;
                    playerY.value += velocityY.value;

                    if (playerY.value < 0) {
                        playerY.value = 0;
                        velocityY.value = 0;
                        isJumping = false;
                    }

                    if (!checkCollision()) {
                        isJumping = true;
                    }
                    requestAnimationFrame(update);
                };

                document.addEventListener('keydown', (e) => {
                    if (e.code === 'Space' && !isJumping) {
                        velocityY.value = jumpForce;
                        isJumping = true;
                    }
                });

                update();

                return { playerX, playerY };
            }
        }).mount('#app');
    </script>
</body>
</html>