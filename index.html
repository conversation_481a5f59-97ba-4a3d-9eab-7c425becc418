<!DOCTYPE html>
<html>
<head>
    <title>简单跳跃游戏</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            font-family: 'Arial', sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            overflow: hidden;
        }

        .game-container {
            background: transparent;
            padding: 0;
            margin-bottom: 20px;
        }

        #game {
            width: 800px;
            height: 400px;
            background: linear-gradient(to bottom, #87CEEB 0%, #87CEEB 60%, #90EE90 60%, #90EE90 85%, #D2691E 85%, #D2691E 100%);
            position: relative;
            overflow: hidden;
            border: 4px solid #1a1a1a;
            border-radius: 0;
        }

        .game-world {
            width: 2000px;
            height: 400px;
            position: absolute;
            background: linear-gradient(to bottom, #87CEEB 0%, #87CEEB 60%, #90EE90 60%, #90EE90 85%, #D2691E 85%, #D2691E 100%);
            transition: transform 0.1s ease-out;
        }

        .player {
            width: 24px;
            height: 48px;
            background: #FF0000;
            position: absolute;
            border-radius: 2px;
            border: 2px solid #CC0000;
        }

        .platform {
            width: 100px;
            height: 20px;
            background: #8B4513;
            position: absolute;
            border-radius: 4px;
            border: 2px solid #654321;
        }

        .coin {
            width: 20px;
            height: 20px;
            background: #FFD700;
            position: absolute;
            border-radius: 50%;
            border: 2px solid #FF8C00;
        }

        .obstacle {
            width: 26px;
            height: 26px;
            background: #4B0082;
            position: absolute;
            border-radius: 50%;
            border: 2px solid #2F0052;
        }

        .info-panel {
            background: rgba(26, 26, 26, 0.9);
            color: white;
            padding: 20px 40px;
            border-radius: 0;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            display: flex;
            justify-content: center;
            align-items: center;
            min-width: 800px;
            border: 4px solid #1a1a1a;
        }

        .info-item {
            display: flex;
            align-items: center;
            margin: 0 40px;
        }

        .info-label {
            font-size: 18px;
            color: white;
            margin-right: 10px;
            font-weight: bold;
        }

        .info-value {
            font-size: 18px;
            font-weight: bold;
            color: white;
        }

        .victory {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(26, 26, 26, 0.95);
            color: #FFD700;
            padding: 40px;
            border: 4px solid #FFD700;
            text-align: center;
            font-size: 28px;
            font-weight: bold;
            display: none;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="game-container">
            <div id="game">
                <div class="game-world" :style="{ transform: 'translateX(' + cameraX + 'px)' }">
                    <div class="player" :style="{ bottom: playerY + 'px', left: playerX + 'px' }"></div>

                    <!-- 平台 -->
                    <div v-for="(platform, index) in platforms" :key="'platform-' + index"
                         class="platform"
                         :style="{ bottom: platform.y + 'px', left: platform.x + 'px' }">
                    </div>

                    <!-- 金币 -->
                    <div v-for="(coin, index) in visibleCoins" :key="'coin-' + index"
                         class="coin"
                         :style="{ bottom: coin.y + 'px', left: coin.x + 'px' }">
                    </div>

                    <!-- 障碍物 -->
                    <div v-for="(obstacle, index) in obstacles" :key="'obstacle-' + index"
                         class="obstacle"
                         :style="{ bottom: obstacle.y + 'px', left: obstacle.x + 'px' }">
                    </div>
                </div>

                <div class="victory" :style="{ display: showVictory ? 'block' : 'none' }">
                    🎉 恭喜！通关了！🎉<br>
                    <small>你收集了所有金币！</small>
                </div>
            </div>
        </div>

        <div class="info-panel">
            <div class="info-item">
                <div class="info-label">Score:</div>
                <div class="info-value">{{ coins }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Coins:</div>
                <div class="info-value">{{ coins }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Lives:</div>
                <div class="info-value">{{ remainingCoins }}</div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/vue@3"></script>
    <script>
        const { createApp, ref } = Vue;

        createApp({
            setup() {
                const playerX = ref(50);
                const playerY = ref(0);
                const velocityY = ref(0);
                const coins = ref(0);
                const targetCoins = ref(8);
                const remainingCoins = ref(8);
                const showVictory = ref(false);
                const cameraX = ref(0);

                const gravity = 0.8;
                const jumpForce = 15;
                const moveSpeed = 4.5;
                let isOnGround = true;

                // 游戏区域和平台数据
                const gameWidth = 800;
                const worldWidth = 2000;
                const gameHeight = 400;
                const playerWidth = 24;
                const playerHeight = 24;

                // 扩展的平台布局
                const platforms = ref([
                    { x: 120, y: 80, width: 100, height: 20 },
                    { x: 300, y: 140, width: 100, height: 20 },
                    { x: 500, y: 100, width: 100, height: 20 },
                    { x: 720, y: 180, width: 100, height: 20 },
                    { x: 950, y: 120, width: 100, height: 20 },
                    { x: 1200, y: 200, width: 100, height: 20 },
                    { x: 1450, y: 160, width: 100, height: 20 },
                    { x: 1700, y: 240, width: 100, height: 20 }
                ]);

                // 金币数据 - 平台上的金币 + 随机金币
                const allCoins = [
                    { x: 150, y: 120 },     // 平台1上
                    { x: 330, y: 180 },     // 平台2上
                    { x: 530, y: 140 },     // 平台3上
                    { x: 750, y: 220 },     // 平台4上
                    { x: 980, y: 160 },     // 平台5上
                    { x: 1230, y: 240 },    // 平台6上
                    { x: 250, y: 50 },      // 随机金币1（地面）
                    { x: 850, y: 60 }       // 随机金币2（地面）
                ];

                // 障碍物数据 - 每个平台下方右侧，纵向距离加倍
                const obstacles = ref([
                    { x: 200, y: 20, width: 26, height: 26 },   // 平台1下方右侧 (原y=50, 现y=20)
                    { x: 380, y: 70, width: 26, height: 26 },   // 平台2下方右侧 (原y=110, 现y=70)
                    { x: 580, y: 30, width: 26, height: 26 },   // 平台3下方右侧 (原y=70, 现y=30)
                    { x: 800, y: 100, width: 26, height: 26 },  // 平台4下方右侧 (原y=150, 现y=100)
                    { x: 1030, y: 40, width: 26, height: 26 },  // 平台5下方右侧 (原y=90, 现y=40)
                    { x: 1280, y: 120, width: 26, height: 26 }, // 平台6下方右侧 (原y=170, 现y=120)
                    { x: 1530, y: 80, width: 26, height: 26 },  // 平台7下方右侧 (原y=130, 现y=80)
                    { x: 1780, y: 160, width: 26, height: 26 }  // 平台8下方右侧 (原y=210, 现y=160)
                ]);

                const visibleCoins = ref([...allCoins]);

                // 检查障碍物碰撞
                const checkObstacleCollision = () => {
                    const playerLeft = playerX.value;
                    const playerRight = playerX.value + playerWidth;
                    const playerBottom = playerY.value;
                    const playerTop = playerY.value + playerHeight;

                    for (let obstacle of obstacles.value) {
                        const obstacleLeft = obstacle.x;
                        const obstacleRight = obstacle.x + obstacle.width;
                        const obstacleBottom = obstacle.y;
                        const obstacleTop = obstacle.y + obstacle.height;

                        // 检查碰撞
                        if (playerRight > obstacleLeft && playerLeft < obstacleRight &&
                            playerTop > obstacleBottom && playerBottom < obstacleTop) {
                            console.log('碰撞检测到！玩家位置:', playerX.value, playerY.value, '障碍物位置:', obstacle.x, obstacle.y);
                            return true; // 发生碰撞，返回true
                        }
                    }
                    return false;
                };

                // 更新镜头位置
                const updateCamera = () => {
                    const targetCameraX = -(playerX.value - gameWidth / 2);
                    const maxCameraX = 0;
                    const minCameraX = -(worldWidth - gameWidth);

                    cameraX.value = Math.max(minCameraX, Math.min(maxCameraX, targetCameraX));
                };

                // 检查金币碰撞
                const checkCoinCollision = () => {
                    const playerLeft = playerX.value;
                    const playerRight = playerX.value + playerWidth;
                    const playerBottom = playerY.value;
                    const playerTop = playerY.value + playerHeight;

                    visibleCoins.value = visibleCoins.value.filter(coin => {
                        const coinLeft = coin.x;
                        const coinRight = coin.x + 16; // 金币宽度
                        const coinBottom = coin.y;
                        const coinTop = coin.y + 16; // 金币高度

                        // 检查碰撞
                        if (playerRight > coinLeft && playerLeft < coinRight &&
                            playerTop > coinBottom && playerBottom < coinTop) {
                            coins.value++;
                            remainingCoins.value--;

                            // 检查是否通关
                            if (coins.value >= targetCoins.value) {
                                showVictory.value = true;
                            }

                            return false; // 移除这个金币
                        }
                        return true; // 保留这个金币
                    });
                };

                // 检查玩家是否在平台上
                const checkPlatformCollision = () => {
                    const playerBottom = playerY.value;
                    const playerTop = playerY.value + playerHeight;
                    const playerLeft = playerX.value;
                    const playerRight = playerX.value + playerWidth;

                    let onPlatform = false;

                    // 检查地面
                    if (playerBottom <= 0 && velocityY.value <= 0) {
                        playerY.value = 0;
                        velocityY.value = 0;
                        onPlatform = true;
                    }

                    // 检查平台碰撞
                    platforms.value.forEach(platform => {
                        const platformTop = platform.y + platform.height;
                        const platformBottom = platform.y;
                        const platformLeft = platform.x;
                        const platformRight = platform.x + platform.width;

                        // 检查水平重叠
                        if (playerRight > platformLeft && playerLeft < platformRight) {
                            // 从上方落到平台上
                            if (playerBottom <= platformTop && playerBottom >= platformBottom && velocityY.value <= 0) {
                                playerY.value = platformTop;
                                velocityY.value = 0;
                                onPlatform = true;
                            }
                        }
                    });

                    return onPlatform;
                };

                // 游戏主循环
                const update = () => {
                    // 如果已经通关，停止游戏逻辑
                    if (showVictory.value) {
                        return;
                    }

                    // 应用重力
                    if (!isOnGround) {
                        velocityY.value -= gravity;
                    }

                    // 更新Y位置
                    playerY.value += velocityY.value;

                    // 检查碰撞
                    isOnGround = checkPlatformCollision();
                    checkCoinCollision();

                    // 更新镜头
                    updateCamera();

                    // 边界检查
                    if (playerX.value < 0) {
                        playerX.value = 0;
                    }
                    if (playerX.value > worldWidth - playerWidth) {
                        playerX.value = worldWidth - playerWidth;
                    }

                    requestAnimationFrame(update);
                };

                // 键盘控制
                const keys = {};

                document.addEventListener('keydown', (e) => {
                    keys[e.code] = true;

                    // 跳跃 (W键或空格键)
                    if ((e.code === 'KeyW' || e.code === 'Space') && isOnGround) {
                        velocityY.value = jumpForce;
                        isOnGround = false;
                        e.preventDefault(); // 防止空格键滚动页面
                    }

                    // 防止方向键滚动页面
                    if (['KeyW', 'KeyA', 'KeyS', 'KeyD', 'Space', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.code)) {
                        e.preventDefault();
                    }
                });

                document.addEventListener('keyup', (e) => {
                    keys[e.code] = false;
                });

                // 持续移动处理
                const handleMovement = () => {
                    if (showVictory.value) {
                        return;
                    }

                    if (keys['KeyA']) {
                        const oldX = playerX.value;
                        playerX.value -= moveSpeed;
                        // 检查移动后是否碰到障碍物
                        if (checkObstacleCollision()) {
                            playerX.value = oldX; // 恢复到移动前的位置
                        }
                    }
                    if (keys['KeyD']) {
                        const oldX = playerX.value;
                        playerX.value += moveSpeed;
                        // 检查移动后是否碰到障碍物
                        if (checkObstacleCollision()) {
                            playerX.value = oldX; // 恢复到移动前的位置
                        }
                    }
                    requestAnimationFrame(handleMovement);
                };

                // 启动游戏循环
                update();
                handleMovement();

                return {
                    playerX,
                    playerY,
                    coins,
                    targetCoins,
                    remainingCoins,
                    visibleCoins,
                    showVictory,
                    cameraX,
                    platforms,
                    obstacles
                };
            }
        }).mount('#app');
    </script>
</body>
</html>