<!DOCTYPE html>
<html>
<head>
    <title>简单跳跃游戏</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            font-family: 'Arial', sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }

        .game-container {
            background: transparent;
            padding: 0;
            margin-bottom: 20px;
        }

        #game {
            width: 800px;
            height: 400px;
            background: linear-gradient(to bottom, #87CEEB 0%, #87CEEB 60%, #90EE90 60%, #90EE90 85%, #D2691E 85%, #D2691E 100%);
            position: relative;
            overflow: hidden;
            border: 4px solid #1a1a1a;
            border-radius: 0;
        }

        .player {
            width: 24px;
            height: 24px;
            background: #FF0000;
            position: absolute;
            border-radius: 2px;
            border: 2px solid #CC0000;
        }

        .platform {
            width: 100px;
            height: 20px;
            background: #8B4513;
            position: absolute;
            border-radius: 4px;
            border: 2px solid #654321;
        }

        .coin {
            width: 20px;
            height: 20px;
            background: #FFD700;
            position: absolute;
            border-radius: 50%;
            border: 2px solid #FF8C00;
        }

        .info-panel {
            background: rgba(26, 26, 26, 0.9);
            color: white;
            padding: 20px 40px;
            border-radius: 0;
            text-align: left;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            display: flex;
            justify-content: space-between;
            min-width: 800px;
            border: 4px solid #1a1a1a;
        }

        .info-item {
            display: flex;
            align-items: center;
            margin: 0;
        }

        .info-label {
            font-size: 18px;
            color: white;
            margin-right: 10px;
            font-weight: bold;
        }

        .info-value {
            font-size: 18px;
            font-weight: bold;
            color: white;
        }

        .victory {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(26, 26, 26, 0.95);
            color: #FFD700;
            padding: 40px;
            border: 4px solid #FFD700;
            text-align: center;
            font-size: 28px;
            font-weight: bold;
            display: none;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="game-container">
            <div id="game">
                <div class="player" :style="{ bottom: playerY + 'px', left: playerX + 'px' }"></div>
                <div class="platform" style="bottom: 80px; left: 120px;"></div>
                <div class="platform" style="bottom: 160px; left: 300px;"></div>
                <div class="platform" style="bottom: 240px; left: 500px;"></div>

                <!-- 金币 -->
                <div v-for="(coin, index) in visibleCoins" :key="index"
                     class="coin"
                     :style="{ bottom: coin.y + 'px', left: coin.x + 'px' }">
                </div>

                <div class="victory" :style="{ display: showVictory ? 'block' : 'none' }">
                    🎉 恭喜！通关了！🎉<br>
                    <small>你收集了所有金币！</small>
                </div>
            </div>
        </div>

        <div class="info-panel">
            <div class="info-item">
                <div class="info-label">Score:</div>
                <div class="info-value">{{ coins }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Coins:</div>
                <div class="info-value">{{ coins }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Lives:</div>
                <div class="info-value">{{ remainingCoins }}</div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/vue@3"></script>
    <script>
        const { createApp, ref } = Vue;

        createApp({
            setup() {
                const playerX = ref(50);
                const playerY = ref(0);
                const velocityY = ref(0);
                const coins = ref(0);
                const targetCoins = ref(6);
                const remainingCoins = ref(6);
                const showVictory = ref(false);

                const gravity = 0.8;
                const jumpForce = 15;
                const moveSpeed = 4.5; // 提高移动速度，便于跳跃
                let isOnGround = true;

                // 游戏区域和平台数据
                const gameWidth = 800;
                const gameHeight = 400;
                const playerWidth = 24;
                const playerHeight = 24;

                const platforms = [
                    { x: 120, y: 80, width: 100, height: 20 },   // 第一平台
                    { x: 300, y: 160, width: 100, height: 20 },  // 第二平台
                    { x: 500, y: 240, width: 100, height: 20 }   // 第三平台
                ];

                // 金币数据 - 固定位置，确保玩家可以触碰到
                const allCoins = [
                    { x: 200, y: 50 },      // 地面上
                    { x: 150, y: 120 },     // 第一个平台上
                    { x: 330, y: 200 },     // 第二个平台上
                    { x: 530, y: 280 },     // 第三个平台上
                    { x: 450, y: 80 },      // 地面上
                    { x: 700, y: 60 }       // 地面上
                ];

                const visibleCoins = ref([...allCoins]);

                // 检查金币碰撞
                const checkCoinCollision = () => {
                    const playerLeft = playerX.value;
                    const playerRight = playerX.value + playerWidth;
                    const playerBottom = playerY.value;
                    const playerTop = playerY.value + playerHeight;

                    visibleCoins.value = visibleCoins.value.filter(coin => {
                        const coinLeft = coin.x;
                        const coinRight = coin.x + 16; // 金币宽度
                        const coinBottom = coin.y;
                        const coinTop = coin.y + 16; // 金币高度

                        // 检查碰撞
                        if (playerRight > coinLeft && playerLeft < coinRight &&
                            playerTop > coinBottom && playerBottom < coinTop) {
                            coins.value++;
                            remainingCoins.value--;

                            // 检查是否通关
                            if (coins.value >= targetCoins.value) {
                                showVictory.value = true;
                            }

                            return false; // 移除这个金币
                        }
                        return true; // 保留这个金币
                    });
                };

                // 检查玩家是否在平台上
                const checkPlatformCollision = () => {
                    const playerBottom = playerY.value;
                    const playerTop = playerY.value + playerHeight;
                    const playerLeft = playerX.value;
                    const playerRight = playerX.value + playerWidth;

                    let onPlatform = false;

                    // 检查地面
                    if (playerBottom <= 0 && velocityY.value <= 0) {
                        playerY.value = 0;
                        velocityY.value = 0;
                        onPlatform = true;
                    }

                    // 检查平台碰撞
                    platforms.forEach(platform => {
                        const platformTop = platform.y + platform.height;
                        const platformBottom = platform.y;
                        const platformLeft = platform.x;
                        const platformRight = platform.x + platform.width;

                        // 检查水平重叠
                        if (playerRight > platformLeft && playerLeft < platformRight) {
                            // 从上方落到平台上
                            if (playerBottom <= platformTop && playerBottom >= platformBottom && velocityY.value <= 0) {
                                playerY.value = platformTop;
                                velocityY.value = 0;
                                onPlatform = true;
                            }
                        }
                    });

                    return onPlatform;
                };

                // 游戏主循环
                const update = () => {
                    // 如果已经通关，停止游戏逻辑
                    if (showVictory.value) {
                        return;
                    }

                    // 应用重力
                    if (!isOnGround) {
                        velocityY.value -= gravity;
                    }

                    // 更新Y位置
                    playerY.value += velocityY.value;

                    // 检查碰撞
                    isOnGround = checkPlatformCollision();
                    checkCoinCollision();

                    // 边界检查
                    if (playerX.value < 0) {
                        playerX.value = 0;
                    }
                    if (playerX.value > gameWidth - playerWidth) {
                        playerX.value = gameWidth - playerWidth;
                    }

                    requestAnimationFrame(update);
                };

                // 键盘控制
                const keys = {};

                document.addEventListener('keydown', (e) => {
                    keys[e.code] = true;

                    // 跳跃 (W键或空格键)
                    if ((e.code === 'KeyW' || e.code === 'Space') && isOnGround) {
                        velocityY.value = jumpForce;
                        isOnGround = false;
                        e.preventDefault(); // 防止空格键滚动页面
                    }
                });

                document.addEventListener('keyup', (e) => {
                    keys[e.code] = false;
                });

                // 持续移动处理
                const handleMovement = () => {
                    if (showVictory.value) {
                        return;
                    }

                    if (keys['KeyA']) {
                        playerX.value -= moveSpeed;
                    }
                    if (keys['KeyD']) {
                        playerX.value += moveSpeed;
                    }
                    requestAnimationFrame(handleMovement);
                };

                // 启动游戏循环
                update();
                handleMovement();

                return {
                    playerX,
                    playerY,
                    coins,
                    targetCoins,
                    remainingCoins,
                    visibleCoins,
                    showVictory
                };
            }
        }).mount('#app');
    </script>
</body>
</html>