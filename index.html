<!DOCTYPE html>
<html>
<head>
    <title>简单跳跃游戏</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
            font-family: 'Arial', sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }

        .game-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        #game {
            width: 800px;
            height: 400px;
            background: linear-gradient(#87CEEB, #90EE90);
            position: relative;
            overflow: hidden;
            border: 3px solid #333;
            border-radius: 8px;
        }

        .player {
            width: 20px;
            height: 20px;
            background: red;
            position: absolute;
            border-radius: 2px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .platform {
            width: 80px;
            height: 15px;
            background: #8B4513;
            position: absolute;
            border-radius: 3px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .coin {
            width: 16px;
            height: 16px;
            background: gold;
            position: absolute;
            border-radius: 50%;
            border: 2px solid #FFD700;
            box-shadow: 0 0 10px rgba(255,215,0,0.5);
            animation: coinSpin 2s linear infinite;
        }

        @keyframes coinSpin {
            0% { transform: rotateY(0deg); }
            100% { transform: rotateY(360deg); }
        }

        .ui {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 18px;
            font-weight: bold;
        }

        .controls {
            background: #333;
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }

        .controls h3 {
            margin: 0 0 10px 0;
            color: #FFD700;
        }

        .control-item {
            display: inline-block;
            margin: 0 15px;
            padding: 5px 10px;
            background: #555;
            border-radius: 4px;
            font-size: 14px;
        }

        .victory {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.9);
            color: #FFD700;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            display: none;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="game-container">
            <div id="game">
                <div class="ui">
                    金币: {{ coins }}/{{ targetCoins }}
                </div>
                <div class="player" :style="{ bottom: playerY + 'px', left: playerX + 'px' }"></div>
                <div class="platform" style="bottom: 60px; left: 150px;"></div>
                <div class="platform" style="bottom: 120px; left: 250px;"></div>
                <div class="platform" style="bottom: 180px; left: 350px;"></div>

                <!-- 金币 -->
                <div v-for="(coin, index) in visibleCoins" :key="index"
                     class="coin"
                     :style="{ bottom: coin.y + 'px', left: coin.x + 'px' }">
                </div>

                <div class="victory" :style="{ display: showVictory ? 'block' : 'none' }">
                    🎉 恭喜！通关了！🎉<br>
                    <small>你收集了所有金币！</small>
                </div>
            </div>
        </div>

        <div class="controls">
            <h3>🎮 游戏控制</h3>
            <div class="control-item">A - 向左移动</div>
            <div class="control-item">D - 向右移动</div>
            <div class="control-item">W/空格 - 跳跃</div>
        </div>
    </div>

    <script src="https://unpkg.com/vue@3"></script>
    <script>
        const { createApp, ref } = Vue;

        createApp({
            setup() {
                const playerX = ref(50);
                const playerY = ref(0);
                const velocityY = ref(0);
                const coins = ref(0);
                const targetCoins = ref(6);
                const showVictory = ref(false);

                const gravity = 0.8;
                const jumpForce = 15;
                const moveSpeed = 2.5; // 降低移动速度
                let isOnGround = true;

                // 游戏区域和平台数据
                const gameWidth = 800;
                const gameHeight = 400;
                const playerWidth = 20;
                const playerHeight = 20;

                const platforms = [
                    { x: 150, y: 60, width: 80, height: 15 },   // 第一平台：x=150-230
                    { x: 250, y: 120, width: 80, height: 15 },  // 第二平台：x=250-330 (缩小间距)
                    { x: 350, y: 180, width: 80, height: 15 }   // 第三平台：x=350-430 (缩小间距)
                ];

                // 金币数据 - 固定位置，确保玩家可以触碰到
                const allCoins = [
                    { x: 100, y: 30 },      // 地面上
                    { x: 180, y: 90 },      // 第一个平台上 (平台顶部75+15)
                    { x: 330, y: 150 },     // 第二个平台上 (平台顶部135+15)
                    { x: 530, y: 210 },     // 第三个平台上 (平台顶部195+15)
                    { x: 400, y: 50 },      // 地面上
                    { x: 650, y: 30 }       // 地面上
                ];

                const visibleCoins = ref([...allCoins]);

                // 检查金币碰撞
                const checkCoinCollision = () => {
                    const playerLeft = playerX.value;
                    const playerRight = playerX.value + playerWidth;
                    const playerBottom = playerY.value;
                    const playerTop = playerY.value + playerHeight;

                    visibleCoins.value = visibleCoins.value.filter(coin => {
                        const coinLeft = coin.x;
                        const coinRight = coin.x + 16; // 金币宽度
                        const coinBottom = coin.y;
                        const coinTop = coin.y + 16; // 金币高度

                        // 检查碰撞
                        if (playerRight > coinLeft && playerLeft < coinRight &&
                            playerTop > coinBottom && playerBottom < coinTop) {
                            coins.value++;

                            // 检查是否通关
                            if (coins.value >= targetCoins.value) {
                                showVictory.value = true;
                            }

                            return false; // 移除这个金币
                        }
                        return true; // 保留这个金币
                    });
                };

                // 检查玩家是否在平台上
                const checkPlatformCollision = () => {
                    const playerBottom = playerY.value;
                    const playerTop = playerY.value + playerHeight;
                    const playerLeft = playerX.value;
                    const playerRight = playerX.value + playerWidth;

                    let onPlatform = false;

                    // 检查地面
                    if (playerBottom <= 0 && velocityY.value <= 0) {
                        playerY.value = 0;
                        velocityY.value = 0;
                        onPlatform = true;
                    }

                    // 检查平台碰撞
                    platforms.forEach(platform => {
                        const platformTop = platform.y + platform.height;
                        const platformBottom = platform.y;
                        const platformLeft = platform.x;
                        const platformRight = platform.x + platform.width;

                        // 检查水平重叠
                        if (playerRight > platformLeft && playerLeft < platformRight) {
                            // 从上方落到平台上
                            if (playerBottom <= platformTop && playerBottom >= platformBottom && velocityY.value <= 0) {
                                playerY.value = platformTop;
                                velocityY.value = 0;
                                onPlatform = true;
                            }
                        }
                    });

                    return onPlatform;
                };

                // 游戏主循环
                const update = () => {
                    // 如果已经通关，停止游戏逻辑
                    if (showVictory.value) {
                        return;
                    }

                    // 应用重力
                    if (!isOnGround) {
                        velocityY.value -= gravity;
                    }

                    // 更新Y位置
                    playerY.value += velocityY.value;

                    // 检查碰撞
                    isOnGround = checkPlatformCollision();
                    checkCoinCollision();

                    // 边界检查
                    if (playerX.value < 0) {
                        playerX.value = 0;
                    }
                    if (playerX.value > gameWidth - playerWidth) {
                        playerX.value = gameWidth - playerWidth;
                    }

                    requestAnimationFrame(update);
                };

                // 键盘控制
                const keys = {};

                document.addEventListener('keydown', (e) => {
                    keys[e.code] = true;

                    // 跳跃 (W键或空格键)
                    if ((e.code === 'KeyW' || e.code === 'Space') && isOnGround) {
                        velocityY.value = jumpForce;
                        isOnGround = false;
                        e.preventDefault(); // 防止空格键滚动页面
                    }
                });

                document.addEventListener('keyup', (e) => {
                    keys[e.code] = false;
                });

                // 持续移动处理
                const handleMovement = () => {
                    if (showVictory.value) {
                        return;
                    }

                    if (keys['KeyA']) {
                        playerX.value -= moveSpeed;
                    }
                    if (keys['KeyD']) {
                        playerX.value += moveSpeed;
                    }
                    requestAnimationFrame(handleMovement);
                };

                // 启动游戏循环
                update();
                handleMovement();

                return {
                    playerX,
                    playerY,
                    coins,
                    targetCoins,
                    visibleCoins,
                    showVictory
                };
            }
        }).mount('#app');
    </script>
</body>
</html>