<!DOCTYPE html>
<html>
<head>
    <title>简单跳跃游戏</title>
    <style>
        #game {
            width: 800px;
            height: 400px;
            background: linear-gradient(#87CEEB, #90EE90);
            position: relative;
            overflow: hidden;
            border: 2px solid #000;
        }
        .player {
            width: 20px;
            height: 20px;
            background: red;
            position: absolute;
        }
        .platform {
            width: 80px;
            height: 15px;
            background: brown;
            position: absolute;
        }
        .controls {
            margin-top: 10px;
            font-family: Arial, sans-serif;
        }
    </style>
</head>
<body>
    <div id="app">
        <div id="game">
            <div class="player" :style="{ bottom: playerY + 'px', left: playerX + 'px' }"></div>
            <div class="platform" style="bottom: 80px; left: 150px;"></div>
            <div class="platform" style="bottom: 150px; left: 300px;"></div>
            <div class="platform" style="bottom: 220px; left: 500px;"></div>
        </div>
        <div class="controls">
            <p>控制说明：A/D - 左右移动，W/空格 - 跳跃</p>
        </div>
    </div>

    <script src="https://unpkg.com/vue@3"></script>
    <script>
        const { createApp, ref } = Vue;

        createApp({
            setup() {
                const playerX = ref(50);
                const playerY = ref(0);
                const velocityY = ref(0);
                const gravity = 0.8;
                const jumpForce = 15;
                const moveSpeed = 5;
                let isOnGround = true;

                // 游戏区域和平台数据
                const gameWidth = 800;
                const gameHeight = 400;
                const playerWidth = 20;
                const playerHeight = 20;

                const platforms = [
                    { x: 150, y: 80, width: 80, height: 15 },
                    { x: 300, y: 150, width: 80, height: 15 },
                    { x: 500, y: 220, width: 80, height: 15 }
                ];

                // 检查玩家是否在平台上
                const checkPlatformCollision = () => {
                    const playerBottom = playerY.value;
                    const playerTop = playerY.value + playerHeight;
                    const playerLeft = playerX.value;
                    const playerRight = playerX.value + playerWidth;

                    let onPlatform = false;

                    // 检查地面
                    if (playerBottom <= 0 && velocityY.value <= 0) {
                        playerY.value = 0;
                        velocityY.value = 0;
                        onPlatform = true;
                    }

                    // 检查平台碰撞
                    platforms.forEach(platform => {
                        const platformTop = platform.y + platform.height;
                        const platformBottom = platform.y;
                        const platformLeft = platform.x;
                        const platformRight = platform.x + platform.width;

                        // 检查水平重叠
                        if (playerRight > platformLeft && playerLeft < platformRight) {
                            // 从上方落到平台上
                            if (playerBottom <= platformTop && playerBottom >= platformBottom && velocityY.value <= 0) {
                                playerY.value = platformTop;
                                velocityY.value = 0;
                                onPlatform = true;
                            }
                        }
                    });

                    return onPlatform;
                };

                // 游戏主循环
                const update = () => {
                    // 应用重力
                    if (!isOnGround) {
                        velocityY.value -= gravity;
                    }

                    // 更新Y位置
                    playerY.value += velocityY.value;

                    // 检查碰撞
                    isOnGround = checkPlatformCollision();

                    // 边界检查
                    if (playerX.value < 0) {
                        playerX.value = 0;
                    }
                    if (playerX.value > gameWidth - playerWidth) {
                        playerX.value = gameWidth - playerWidth;
                    }

                    requestAnimationFrame(update);
                };

                // 键盘控制
                const keys = {};

                document.addEventListener('keydown', (e) => {
                    keys[e.code] = true;

                    // 跳跃 (W键或空格键)
                    if ((e.code === 'KeyW' || e.code === 'Space') && isOnGround) {
                        velocityY.value = jumpForce;
                        isOnGround = false;
                        e.preventDefault(); // 防止空格键滚动页面
                    }
                });

                document.addEventListener('keyup', (e) => {
                    keys[e.code] = false;
                });

                // 持续移动处理
                const handleMovement = () => {
                    if (keys['KeyA']) {
                        playerX.value -= moveSpeed;
                    }
                    if (keys['KeyD']) {
                        playerX.value += moveSpeed;
                    }
                    requestAnimationFrame(handleMovement);
                };

                // 启动游戏循环
                update();
                handleMovement();

                return { playerX, playerY };
            }
        }).mount('#app');
    </script>
</body>
</html>